import { Response, NextFunction } from 'express'
import { StatusCodes } from 'http-status-codes'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { Log } from '../../database/entities/Log'
import { Brackets } from 'typeorm'

const getAllLogs = async (req: any, res: Response, next: NextFunction) => {
  try {
    const { user } = req
    if (!user.id) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'User not found')
    }

    const appServer = getRunningExpressApp()
    const foundUser = await appServer.AppDataSource.getRepository('User').findOneBy({ id: user.id })
    if (!foundUser) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'User not found')
    }

    // Chỉ admin mới được xem logs
    if (foundUser.role !== 'MASTER_ADMIN' && foundUser.role !== 'SITE_ADMIN' && foundUser.role !== 'ADMIN') {
      throw new InternalFlowiseError(StatusCodes.FORBIDDEN, 'Access denied - Admin only')
    }

    const page = parseInt(req.query?.page as string) || 1
    const pageSize = parseInt(req.query?.pageSize as string) || 15
    const target_type = req.query?.target_type as string
    const username = req.query?.username as string
    const action = req.query?.action as string
    const content = req.query?.content as string
    const fromDate = req.query?.fromDate as string
    const toDate = req.query?.toDate as string

    let query = appServer.AppDataSource.getRepository(Log).createQueryBuilder('log')

    // Filter by action (create, update, delete)
    if (action) {
      query = query.andWhere('log.action = :action', { action })
    }

    // Filter by target type (agent, folder, file)
    if (target_type) {
      query = query.andWhere('log.target_type = :target_type', { target_type })
    }

    // Filter by username (partial match)
    if (username) {
      query = query.andWhere('log.username ILIKE :username', { username: `%${username}%` })
    }

    // Filter by date range
    if (fromDate) {
      query = query.andWhere('log.created_at >= :fromDate', { fromDate: `${fromDate} 00:00:00` })
    }
    if (toDate) {
      query = query.andWhere('log.created_at <= :toDate', { toDate: `${toDate} 23:59:59` })
    }

    // Search in target_name and changes content
    if (content) {
      query = query.andWhere(
        new Brackets((qb) => {
          qb.where('log.target_name ILIKE :content', {
            content: `%${content}%`
          })
        })
      )
    }

    const totalCount = await query.getCount()

    query = query
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .orderBy('log.created_at', 'DESC')

    const logs = await query.getMany()

    return res.json({
      data: logs,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    })
  } catch (error) {
    next(error)
  }
}

const getLogsByAgent = async (req: any, res: Response, next: NextFunction) => {
  try {
    const { user } = req
    const { agentId } = req.params

    if (!user.id) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'User not found')
    }

    const appServer = getRunningExpressApp()
    const foundUser = await appServer.AppDataSource.getRepository('User').findOneBy({ id: user.id })
    if (!foundUser) {
      throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'User not found')
    }

    const page = parseInt(req.query?.page as string) || 1
    const pageSize = parseInt(req.query?.pageSize as string) || 20

    let query = appServer.AppDataSource.getRepository(Log)
      .createQueryBuilder('log')
      .where('log.target_id = :agentId AND log.target_type = :targetType', { agentId, targetType: 'agent' })

    const totalCount = await query.getCount()

    query = query
      .skip((page - 1) * pageSize)
      .take(pageSize)
      .orderBy('log.created_at', 'DESC')

    const logs = await query.getMany()

    return res.json({
      data: logs,
      pagination: {
        page,
        pageSize,
        totalCount,
        totalPages: Math.ceil(totalCount / pageSize)
      }
    })
  } catch (error) {
    next(error)
  }
}

export default {
  getAllLogs,
  getLogsByAgent
}
