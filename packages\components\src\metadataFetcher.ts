/**
 * Interface for S3 Explorer metadata response
 */
export interface IS3MetadataResponse {
  content: {
    metadataAttributes: Record<string, string>
  }
}

/**
 * Fetch metadata for a given prefix from S3 Explorer API
 * @param prefix - The S3 prefix to fetch metadata for
 * @param baseUrl - The base URL for the API (defaults to current origin)
 * @param timeout - Request timeout in milliseconds (defaults to 5000)
 * @returns Promise<IS3MetadataResponse | null> - Returns metadata or null if not found/error
 */
export async function fetchPrefixMetadata(prefix: string, baseUrl?: string, timeout: number = 5000): Promise<IS3MetadataResponse | null> {
  try {
    const apiBaseUrl = baseUrl || `http://localhost:${process.env.PORT || 3000}`
    const metadataPath = `${prefix}.metadata.json`
    const apiUrl = `${apiBaseUrl}/s3-explorer/api/read-json?path=${encodeURIComponent(metadataPath)}`

    if (process.env.DEBUG === 'true') {
      console.info(`[MetadataFetcher] Fetching metadata for prefix: ${prefix}`)
      console.info(`[MetadataFetcher] API URL: ${apiUrl}`)
    }

    // Create AbortController for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      },
      signal: controller.signal
    })

    clearTimeout(timeoutId)

    if (!response.ok) {
      if (response.status === 404) {
        if (process.env.DEBUG === 'true') {
          console.info(`[MetadataFetcher] Metadata file not found for prefix: ${prefix}`)
        }
        return null
      }
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()

    if (!data || !data.content || !data.content.metadataAttributes) {
      console.warn(`[MetadataFetcher] Invalid metadata structure for prefix: ${prefix}`)
      return null
    }

    if (process.env.DEBUG === 'true') {
      console.info(`[MetadataFetcher] Successfully fetched metadata for prefix: ${prefix}`)
    }

    return data as IS3MetadataResponse
  } catch (error: any) {
    if (error.name === 'AbortError') {
      console.warn(`[MetadataFetcher] Timeout fetching metadata for prefix: ${prefix}`)
    } else {
      console.warn(`[MetadataFetcher] Error fetching metadata for prefix ${prefix}: ${error.message}`)
    }
    return null
  }
}

/**
 * Fetch metadata for multiple prefixes in parallel
 * @param prefixes - Array of S3 prefixes to fetch metadata for
 * @param baseUrl - The base URL for the API (defaults to current origin)
 * @param timeout - Request timeout in milliseconds (defaults to 5000)
 * @returns Promise<Map<string, IS3MetadataResponse>> - Map of prefix to metadata
 */
export async function fetchMultiplePrefixMetadata(
  prefixes: string[],
  baseUrl?: string,
  timeout: number = 5000
): Promise<Map<string, IS3MetadataResponse>> {
  const metadataMap = new Map<string, IS3MetadataResponse>()

  if (prefixes.length === 0) {
    return metadataMap
  }

  try {
    const metadataPromises = prefixes.map(async (prefix) => {
      const metadata = await fetchPrefixMetadata(prefix, baseUrl, timeout)
      return { prefix, metadata }
    })

    const results = await Promise.allSettled(metadataPromises)

    results.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value.metadata) {
        metadataMap.set(result.value.prefix, result.value.metadata)
      } else if (result.status === 'rejected') {
        console.warn(`[MetadataFetcher] Failed to fetch metadata for prefix ${prefixes[index]}: ${result.reason}`)
      }
    })

    if (process.env.DEBUG === 'true') {
      console.info(`[MetadataFetcher] Fetched metadata for ${metadataMap.size}/${prefixes.length} prefixes`)
    }

    return metadataMap
  } catch (error: any) {
    console.error(`[MetadataFetcher] Error in fetchMultiplePrefixMetadata: ${error.message}`)
    return metadataMap
  }
}

/**
 * Extract prefix from S3 source URI
 * @param sourceUri - The S3 source URI (e.g., "s3://bucket-name/prefix/file.pdf")
 * @returns The extracted prefix or null if invalid
 */
export function extractPrefixFromSourceUri(sourceUri: string): string | null {
  if (!sourceUri || typeof sourceUri !== 'string') {
    return null
  }

  // Remove s3:// and bucket name to get the prefix
  const match = sourceUri.match(/^s3:\/\/[^/]+\/(.+)$/)
  return match ? match[1] : null
}
