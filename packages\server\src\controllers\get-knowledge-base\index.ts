import { Request, Response, NextFunction } from 'express'
import { KnowledgeBase } from '../../database/entities/IngestionJobs'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'

const getFolderNameByKB = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { knowledgeBaseID } = req.body
    if (!knowledgeBaseID) {
      console.error('knowledgeBaseID is empty')
    }
    const appServer = getRunningExpressApp()
    const response = await appServer.AppDataSource.getRepository(KnowledgeBase).findOne({
      where: {
        knowledge_base_id: knowledgeBaseID
      }
    })
    if (response) {
      res.status(200).json({
        name: response?.folder_name
      })
    } else {
      throw new Error('Knowledge base not found')
    }
  } catch (error) {
    console.log(error)
  }
}

export default {
  getFolderNameByKB
}
