import { omit } from 'lodash'
import { ICommonObject, INode, INodeData, INodeOutputsValue, INodeParams } from '../../../src/Interface'
import { TextSplitter } from 'langchain/text_splitter'
import { TextLoader } from 'langchain/document_loaders/fs/text'
import { JSONLinesLoader, JSONLoader } from 'langchain/document_loaders/fs/json'
import { CSVLoader } from '@langchain/community/document_loaders/fs/csv'
import { PDFLoader } from '@langchain/community/document_loaders/fs/pdf'
import { DocxLoader } from '@langchain/community/document_loaders/fs/docx'
import { BaseDocumentLoader } from 'langchain/document_loaders/base'
import { Document } from '@langchain/core/documents'
import { getFileFromStorage } from '../../../src/storageUtils'
import { handleEscapeCharacters, mapMimeTypeToExt } from '../../../src/utils'
import * as XLSX from 'xlsx'
import * as Tesseract from 'tesseract.js'
import { PDFDocument } from 'pdf-lib'
import { pdfToPng } from 'pdf-to-png-converter'

class XLSXLoader extends BaseDocumentLoader {
  constructor(public blob: Blob) {
    super()
  }

  public async load(): Promise<Document[]> {
    const arrayBuffer = await this.blob.arrayBuffer()
    const workbook = XLSX.read(arrayBuffer, { type: 'array' })
    const sheetName = workbook.SheetNames[0]
    const sheet = workbook.Sheets[sheetName]
    const data = XLSX.utils.sheet_to_json(sheet)

    return data.map((row: any) => new Document({ pageContent: JSON.stringify(row), metadata: {} }))
  }
}

class OCRPDFLoader extends BaseDocumentLoader {
  private splitPages: boolean

  constructor(public blob: Blob, options?: { splitPages?: boolean }) {
    super()
    this.splitPages = options?.splitPages ?? true
  }

  public async load(): Promise<Document[]> {
    try {
      const documents: Document[] = []

      const imageBuffers = await this.convertPDFBlobToImages(this.blob)

      if (!imageBuffers || imageBuffers?.length === 0) {
        throw new Error('No images found in PDF')
      }
      for await (const [i, imageBuffer] of imageBuffers.entries()) {
        try {
          try {
            const { data } = await Tesseract.recognize(imageBuffer, 'vie')
            const finalText = data?.text?.trim() || ''
            console.log('🚀 ~ File.ts:57 ~ OCRPDFLoader ~ forawait ~ finalText:', finalText)

            documents.push(
              new Document({
                pageContent: JSON.stringify(finalText),
                metadata: {}
              })
            )
          } catch (renderError) {
            console.error(`Error rendering PDF page ${i} for OCR:`, renderError)
          }
        } catch (pageError) {
          console.error(`Error processing page ${i}:`, pageError)
        }
      }

      return documents
    } catch (error) {
      console.error('Error processing PDF with OCR:', error)
      throw new Error(`Failed to load PDF with OCR: ${error.message}`)
    }
  }

  // Helper method to extract image data from PDF pages
  private async convertPDFBlobToImages(pdfBlob: Blob) {
    try {
      const arrayBuffer = await pdfBlob.arrayBuffer()

      const pdfBuffer = Buffer.from(arrayBuffer)

      const pdfDoc = await PDFDocument.load(pdfBuffer)
      const pageCount = pdfDoc.getPages().length
      console.log('🚀 ~ File.ts:88 ~ OCRPDFLoader ~ convertPDFBlobToImages ~ pageCount:', pageCount)

      const imageBuffers: Buffer[] = []

      const pagesToProcess = Array.from({ length: pageCount }, (_, i) => i + 1)
      const pngPages = await pdfToPng(pdfBuffer, {
        disableFontFace: false,
        useSystemFonts: false,
        pagesToProcess: pagesToProcess,
        viewportScale: 2.0
      })
      console.log('🚀 ~ File.ts:133 ~ OCRPDFLoader ~ convertPDFBlobToImages ~ pngPages:', pngPages)

      for await (const page of pngPages) {
        console.log('🚀 ~ File.ts:135 ~ OCRPDFLoader ~ forawait ~ page:', page)
        try {
          imageBuffers.push(page.content)
        } catch (error) {
          console.error(`Error converting page ${page}:`, error)
        }
      }

      return imageBuffers
    } catch (error) {
      console.error('Error extracting images from PDF:', error)
    }
  }
}

class File_DocumentLoaders implements INode {
  label: string
  name: string
  version: number
  description: string
  type: string
  icon: string
  category: string
  baseClasses: string[]
  inputs: INodeParams[]
  outputs: INodeOutputsValue[]

  constructor() {
    this.label = 'File Loader'
    this.name = 'fileLoader'
    this.version = 2.0
    this.type = 'Document'
    this.icon = 'file.svg'
    this.category = 'Document Loaders'
    this.description = `A generic file loader that can load txt, json, csv, docx, pdf, and other files`
    this.baseClasses = [this.type]
    this.inputs = [
      {
        label: 'File',
        name: 'file',
        type: 'file',
        fileType: '*'
      },
      {
        label: 'Text Splitter',
        name: 'textSplitter',
        type: 'TextSplitter',
        optional: true
      },
      {
        label: 'Pdf Usage',
        name: 'pdfUsage',
        type: 'options',
        description: 'Only when loading PDF files',
        options: [
          {
            label: 'One document per page',
            name: 'perPage'
          },
          {
            label: 'One document per file',
            name: 'perFile'
          }
        ],
        default: 'perPage',
        optional: true,
        additionalParams: true
      },
      {
        label: 'JSONL Pointer Extraction',
        name: 'pointerName',
        type: 'string',
        description: 'Only when loading JSONL files',
        placeholder: '<pointerName>',
        optional: true,
        additionalParams: true
      },
      {
        label: 'Additional Metadata',
        name: 'metadata',
        type: 'json',
        description: 'Additional metadata to be added to the extracted documents',
        optional: true,
        additionalParams: true
      },
      {
        label: 'Omit Metadata Keys',
        name: 'omitMetadataKeys',
        type: 'string',
        rows: 4,
        description:
          'Each document loader comes with a default set of metadata keys that are extracted from the document. You can use this field to omit some of the default metadata keys. The value should be a list of keys, seperated by comma. Use * to omit all metadata keys execept the ones you specify in the Additional Metadata field',
        placeholder: 'key1, key2, key3.nestedKey1',
        optional: true,
        additionalParams: true
      }
    ]
    this.outputs = [
      {
        label: 'Document',
        name: 'document',
        description: 'Array of document objects containing metadata and pageContent',
        baseClasses: [...this.baseClasses, 'json']
      },
      {
        label: 'Text',
        name: 'text',
        description: 'Concatenated string from pageContent of documents',
        baseClasses: ['string', 'json']
      }
    ]
  }

  async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
    const textSplitter = nodeData.inputs?.textSplitter as TextSplitter
    const fileBase64 = nodeData.inputs?.file as string
    const metadata = nodeData.inputs?.metadata
    const pdfUsage = nodeData.inputs?.pdfUsage
    const pointerName = nodeData.inputs?.pointerName as string
    const _omitMetadataKeys = nodeData.inputs?.omitMetadataKeys as string
    const output = nodeData.outputs?.output as string

    let omitMetadataKeys: string[] = []
    if (_omitMetadataKeys) {
      omitMetadataKeys = _omitMetadataKeys.split(',').map((key) => key.trim())
    }

    let files: string[] = []
    const fileBlobs: { blob: Blob; ext: string }[] = []

    const totalFiles = getOverrideFileInputs(nodeData) || fileBase64
    if (totalFiles.startsWith('FILE-STORAGE::')) {
      const fileName = totalFiles.replace('FILE-STORAGE::', '')
      if (fileName.startsWith('[') && fileName.endsWith(']')) {
        files = JSON.parse(fileName)
      } else {
        files = [fileName]
      }
      const chatflowid = options.chatflowid

      const retrieveAttachmentChatId = options.retrieveAttachmentChatId
      if (retrieveAttachmentChatId) {
        for (const file of files) {
          if (!file) continue
          const fileData = await getFileFromStorage(file, chatflowid, options.chatId)
          const blob = new Blob([fileData])
          fileBlobs.push({ blob, ext: file.split('.').pop() || '' })
        }
      } else {
        for (const file of files) {
          if (!file) continue
          const fileData = await getFileFromStorage(file, chatflowid)
          const blob = new Blob([fileData])
          fileBlobs.push({ blob, ext: file.split('.').pop() || '' })
        }
      }
    } else {
      if (totalFiles.startsWith('[') && totalFiles.endsWith(']')) {
        files = JSON.parse(totalFiles)
      } else {
        files = [totalFiles]
      }

      for (const file of files) {
        if (!file) continue
        const splitDataURI = file.split(',')
        splitDataURI.pop()
        const bf = Buffer.from(splitDataURI.pop() || '', 'base64')
        const blob = new Blob([bf])

        let extension = ''
        // eslint-disable-next-line no-useless-escape
        const match = file.match(/^data:([A-Za-z-+\/]+);base64,/)

        if (!match) {
          fileBlobs.push({
            blob,
            ext: extension
          })
        } else {
          const mimeType = match[1]
          fileBlobs.push({
            blob,
            ext: mapMimeTypeToExt(mimeType)
          })
        }
      }
    }

    // Add a function to check if PDF content is primarily image-based
    async function isPDFImageBased(blob: Blob): Promise<boolean> {
      try {
        // Try to extract text using standard PDF loader
        const standardLoader = new PDFLoader(blob, {
          // @ts-ignore
          pdfjs: () => import('pdf-parse/lib/pdf.js/v1.10.100/build/pdf.js')
        })
        const docs = await standardLoader.load()

        // If average text per page is below threshold, consider it image-based
        return docs.length === 0
      } catch (error) {
        console.warn('Error analyzing PDF content:', error)
        return false
      }
    }

    const loader = new MultiFileLoader(fileBlobs, {
      json: (blob) => new JSONLoader(blob),
      jsonl: (blob) => new JSONLinesLoader(blob, '/' + pointerName.trim()),
      txt: (blob) => new TextLoader(blob),
      csv: (blob) => new CSVLoader(blob),
      xls: (blob) => new XLSXLoader(blob),
      xlsx: (blob) => new XLSXLoader(blob),
      docx: (blob) => new DocxLoader(blob),
      doc: (blob) => new DocxLoader(blob),
      pdf: async (blob) => {
        // Initialize with user preference if available
        let shouldUseOCR

        // Auto-detect if not explicitly set by user
        if (shouldUseOCR === undefined) {
          shouldUseOCR = await isPDFImageBased(blob)
        }

        if (shouldUseOCR) {
          return pdfUsage === 'perFile' ? new OCRPDFLoader(blob, { splitPages: false }) : new OCRPDFLoader(blob, { splitPages: true })
        } else {
          return pdfUsage === 'perFile'
            ? // @ts-ignore
              new PDFLoader(blob, { splitPages: false, pdfjs: () => import('pdf-parse/lib/pdf.js/v1.10.100/build/pdf.js') })
            : // @ts-ignore
              new PDFLoader(blob, { pdfjs: () => import('pdf-parse/lib/pdf.js/v1.10.100/build/pdf.js') })
        }
      },
      '': (blob) => new TextLoader(blob)
    })

    let docs = []

    if (textSplitter) {
      docs = await loader.load()
      docs = await textSplitter.splitDocuments(docs)
    } else {
      docs = await loader.load()
    }

    if (metadata) {
      const parsedMetadata = typeof metadata === 'object' ? metadata : JSON.parse(metadata)
      docs = docs.map((doc) => ({
        ...doc,
        metadata:
          _omitMetadataKeys === '*'
            ? {
                ...parsedMetadata
              }
            : omit(
                {
                  ...doc.metadata,
                  ...parsedMetadata
                },
                omitMetadataKeys
              )
      }))
    } else {
      docs = docs.map((doc) => ({
        ...doc,
        metadata:
          _omitMetadataKeys === '*'
            ? {}
            : omit(
                {
                  ...doc.metadata
                },
                omitMetadataKeys
              )
      }))
    }

    if (output === 'document') {
      return docs
    } else {
      let finaltext = ''
      for (const doc of docs) {
        finaltext += `${doc.pageContent}\n`
      }
      return handleEscapeCharacters(finaltext, false)
    }
  }
}

const getOverrideFileInputs = (nodeData: INodeData) => {
  const txtFileBase64 = nodeData.inputs?.txtFile as string
  const pdfFileBase64 = nodeData.inputs?.pdfFile as string
  const jsonFileBase64 = nodeData.inputs?.jsonFile as string
  const csvFileBase64 = nodeData.inputs?.csvFile as string
  const jsonlinesFileBase64 = nodeData.inputs?.jsonlinesFile as string
  const docxFileBase64 = nodeData.inputs?.docxFile as string
  const yamlFileBase64 = nodeData.inputs?.yamlFile as string

  const removePrefix = (storageFile: string): string[] => {
    const fileName = storageFile.replace('FILE-STORAGE::', '')
    if (fileName.startsWith('[') && fileName.endsWith(']')) {
      return JSON.parse(fileName)
    }
    return [fileName]
  }

  const files: string[] = []
  if (txtFileBase64) {
    files.push(...removePrefix(txtFileBase64))
  }
  if (pdfFileBase64) {
    files.push(...removePrefix(pdfFileBase64))
  }
  if (jsonFileBase64) {
    files.push(...removePrefix(jsonFileBase64))
  }
  if (csvFileBase64) {
    files.push(...removePrefix(csvFileBase64))
  }
  if (jsonlinesFileBase64) {
    files.push(...removePrefix(jsonlinesFileBase64))
  }
  if (docxFileBase64) {
    files.push(...removePrefix(docxFileBase64))
  }
  if (yamlFileBase64) {
    files.push(...removePrefix(yamlFileBase64))
  }

  return files.length ? `FILE-STORAGE::${JSON.stringify(files)}` : ''
}

interface LoadersMapping {
  [extension: string]: (blob: Blob) => BaseDocumentLoader | Promise<BaseDocumentLoader>
}

class MultiFileLoader extends BaseDocumentLoader {
  constructor(public fileBlobs: { blob: Blob; ext: string }[], public loaders: LoadersMapping) {
    super()

    if (Object.keys(loaders).length === 0) {
      throw new Error('Must provide at least one loader')
    }
  }

  public async load(): Promise<Document[]> {
    const documents: Document[] = []

    for (const fileBlob of this.fileBlobs) {
      const loaderFactory = this.loaders[fileBlob.ext]
      if (loaderFactory) {
        const loader = await Promise.resolve(loaderFactory(fileBlob.blob))
        documents.push(...(await loader.load()))
      } else {
        const loader = new TextLoader(fileBlob.blob)
        try {
          documents.push(...(await loader.load()))
        } catch (error) {
          throw new Error(`Error loading file`)
        }
      }
    }

    return documents
  }
}

module.exports = { nodeClass: File_DocumentLoaders }
