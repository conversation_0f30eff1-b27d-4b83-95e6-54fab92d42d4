import { StatusCodes } from 'http-status-codes'
import { v4 as uuidv4 } from 'uuid'
import { QA } from '../../database/entities/AQ'
import { ChatFlow } from '../../database/entities/ChatFlow'
import { ChatMessage } from '../../database/entities/ChatMessage'
import { Label } from '../../database/entities/Label'
import { User } from '../../database/entities/User'
import { InternalFlowiseError } from '../../errors/internalFlowiseError'
import { getErrorMessage } from '../../errors/utils'
import { getRunningExpressApp } from '../../utils/getRunningExpressApp'
import {
  addDocuments,
  addUserProvidedEmbedderSetting,
  basicSearch,
  createIndex,
  deleteAllDocuments,
  deleteDocuments,
  deleteIndex,
  getAllDocuments,
  getDocumentById,
  updateDocument,
  vectorSearch
} from '../meilisearch/meilisearch'
import { analyze, classifyQuestions } from '../model/index'
import { generateSystemPromptModel } from '../model'
import { Request } from 'express'

const validateUser = async (req: any) => {
  const { user } = req
  if (!user.id) {
    throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: documentStoreServices.getAllDocumentStores - User not found')
  }
  const appServer = getRunningExpressApp()
  const foundUser = await appServer.AppDataSource.getRepository(User).findOneBy({ id: user.id })
  if (!foundUser) {
    throw new InternalFlowiseError(StatusCodes.UNAUTHORIZED, 'Error: documentStoreServices.getAllDocumentStores - User not found')
  }
}

// interface FaqData {
//   _id?: string
//   question: string
//   answer: string
//   chatflowId: string
//   createdDate: Date
//   updatedDate: Date
// }

const saveFaq = async (req: any) => {
  try {
    const { question, answer, chatflowId } = req.body

    if (!question) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: faqsService.saveFaq - question or answer not provided!')
    }

    const document = {
      id: uuidv4(),
      question,
      answer,
      chatflowId,
      createdDate: new Date(),
      updatedDate: new Date()
    }

    await validateUser(req)
    await addDocuments(`document_${chatflowId}`, [document])
    return document
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.saveFaq - ${getErrorMessage(error)}`)
  }
}

const importFaqs = async (req: any) => {
  try {
    const { faqsData, chatflowId } = req.body
    console.log('🚀 ~ index.ts:80 ~ importFaqs ~ faqsData:', faqsData)

    const documents = faqsData.map((faq: any) => ({
      id: uuidv4(),
      question: faq.question,
      answer: faq.answer,
      chatflowId,
      createdDate: new Date(),
      updatedDate: new Date()
    }))
    await validateUser(req)
    await addDocuments(`document_${chatflowId}`, documents)
    return documents
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.importFaqs - ${getErrorMessage(error)}`)
  }
}

const getAllFaqs = async (req: any) => {
  const chatflowId = req.query?.chatflowId
  let { limit, offset } = req?.query || {}

  limit = parseInt(limit, 10) || 20
  offset = parseInt(offset, 10) || 0

  try {
    await validateUser(req)
    return await getAllDocuments(`document_${chatflowId}`, limit, offset)
  } catch (error: any) {
    if (error.code === 'index_not_found' && error.httpStatus === 404) {
      await createIndex(`document_${chatflowId}`)
      return await getAllDocuments(`document_${chatflowId}`, limit, offset)
    }
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `${getErrorMessage(error)}`)
  }
}

const getFaqById = async (id: string, chatflowId: string) => {
  try {
    return await getDocumentById(`document_${chatflowId}`, id)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.getFaqById - ${getErrorMessage(error)}`)
  }
}

const updateFaq = async (req: any) => {
  try {
    const { question, answer, chatflowId } = req.body

    const { id } = req.params

    if (!chatflowId || !id) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: faqsService.updateFaq - chatflowId or id not provided!')
    }

    if (!question || !answer) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: faqsService.updateFaq - question or answer not provided!')
    }

    const newDocument = {
      id: id,
      question,
      answer,
      chatflowId,
      updatedDate: new Date()
    }

    await validateUser(req)
    await updateDocument(`document_${chatflowId}`, newDocument)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.updateFaq - ${getErrorMessage(error)}`)
  }
}

const deleteFaq = async (req: any) => {
  try {
    const { chatflowId, id } = req.params

    if (!chatflowId || !id) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: faqsService.deleteFaq - chatflowId or id not provided!')
    }

    await validateUser(req)
    await deleteDocuments(`document_${chatflowId}`, [id])
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.deleteFaq - ${getErrorMessage(error)}`)
  }
}

const deleteAllFaqs = async (req: any) => {
  try {
    const chatflowId = req.params?.chatflowId
    await validateUser(req)
    await deleteAllDocuments(`document_${chatflowId}`)
    await deleteIndex(`document_${chatflowId}`)

    return { message: 'All FAQs deleted successfully' }
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.deleteAllFaqs - ${getErrorMessage(error)}`)
  }
}

const searchFaqs = async (query: string, searchParams: any, req: any) => {
  try {
    const chatflowId = req.params?.chatflowId
    return await basicSearch(`document_${chatflowId}`, query, searchParams)
    // return await vectorSearch(`document_${chatflowId}`, query, searchParams)
    // return await hybridSearch(`document_${chatflowId}`, query, searchParams)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.searchFaqs - ${getErrorMessage(error)}`)
  }
}

const vectorSearchFaqs = async (query: string, searchParams: any, req: any) => {
  try {
    const chatflowId = req.params?.chatflowId
    return await vectorSearch(`document_${chatflowId}`, query, searchParams)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.vectorSearchFaqs - ${getErrorMessage(error)}`)
  }
}

const deleteIndexService = async (chatflowId: string) => {
  try {
    await deleteIndex(`document_${chatflowId}`)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: faqsService.deleteIndex - ${getErrorMessage(error)}`)
  }
}

const updateSettingsService = async (req: any) => {
  try {
    const { chatflowId } = req.params

    if (!chatflowId) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'chatflowId not provided!')
    }

    await validateUser(req)
    return await addUserProvidedEmbedderSetting(`document_${chatflowId}`)
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `${getErrorMessage(error)}`)
  }
}

const refineFaqFromHistory = async (
  chat_history: any[],
  chatid: string,
  chatflowId: string,
  timeLastCron: string,
  user: User | undefined
) => {
  try {
    const appServer = getRunningExpressApp()
    const QARepo = appServer.AppDataSource.getRepository(QA)
    const foundQa_list = await QARepo.createQueryBuilder('qa')
      .where('qa.chatId = :chatid', { chatid })
      .orderBy('qa.createdDate', 'DESC')
      .limit(10)
      .getMany()

    const qa_list =
      foundQa_list.map((qa) => ({
        id: qa.id,
        question: qa.question,
        answer: qa.answer,
        time: qa.createdDate
      })) || []

    const stringResponse = await analyze(chat_history, qa_list)
    console.log('🚀 ~ index.ts:247 ~ stringResponse:', stringResponse)
    let arrayResponse: {
      id: string
      question: string
      answer: string
      time: Date
    }[] = []
    try {
      arrayResponse = JSON.parse(stringResponse)
    } catch (error) {
      console.log('🚀 ~ index.ts:251 ~ error:', error)
    }
    const newAqsContent = arrayResponse.map((item) =>
      QARepo.create({
        id: item.id || uuidv4(),
        answer: item?.answer || '',
        question: item?.question || '',
        chatflowid: chatflowId,
        chatId: chatid,
        timeLastCron: timeLastCron,
        userName: user?.username || '',
        userId: user?.id,
        createdDate: item.time || new Date(),
        updatedDate: item.time || new Date(),
        question_type: ''
      })
    )

    if (newAqsContent?.length > 0) {
      for (const content of newAqsContent) {
        const existingRecord = await QARepo.findOneBy({ id: content.id })
        if (existingRecord) {
          await QARepo.update({ id: content.id }, content)
        } else {
          await QARepo.save(content)
        }
      }
    }
  } catch (error) {
    console.log('🚀 ~ index.ts:282 ~ error:', error)
  }
}

const getAllMessagesFromTime = async (createdDate: string | Date, chatflowId: string) => {
  try {
    const appServer = getRunningExpressApp()
    console.log('🚀 ~ index.ts:296 ~ getAllMessagesFromTime ~ new Date(createdDate):', new Date(createdDate))

    const messages = await appServer.AppDataSource.getRepository(ChatMessage)
      .createQueryBuilder('chat')
      .leftJoinAndSelect('chat.user', 'user')
      .where('chat.createdDate > :createdDate', { createdDate: new Date(createdDate) })
      .andWhere('chat.chatflowid = :chatflowId', { chatflowId })
      .orderBy('chat.createdDate', 'ASC')
      .getMany()

    const grouped: { [key: string]: { question: string; answer: string; time: Date; id: string }[] } = {}
    let foundUser: User | undefined

    for (let i = 0; i < messages.length; i++) {
      const msg = messages[i]
      const { chatId, role, content, user } = msg

      if (!foundUser) {
        foundUser = user
      }
      if (!grouped[chatId]) {
        grouped[chatId] = []
      }

      if (role === 'userMessage') {
        const nextMsg = messages[i + 1]
        if (nextMsg && nextMsg.chatId === chatId && nextMsg.role === 'apiMessage') {
          grouped[chatId].push({
            question: content,
            answer: nextMsg.content,
            time: nextMsg.createdDate,
            id: uuidv4()
          })
          i++
        }
      }
    }

    const result = Object.entries(grouped).map(([chatId, qaPairs]) => ({
      chatId,
      user: foundUser,
      messages: qaPairs
    }))

    return result
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: chatMessagesService.getAllMessagesByChatflowId - ${getErrorMessage(error)}`
    )
  }
}

const cronJobRefineFaqFromHistory = async (req: any) => {
  try {
    let { chatflowId, minute } = req.body

    if (!chatflowId) {
      chatflowId = '15de7dd3-1cac-4803-ac5a-cd64f13d925d'
    }

    const timeLastCron = new Date(Date.now() - minute * 60 * 1000).toISOString()

    const history15M = await getAllMessagesFromTime(timeLastCron, chatflowId)

    if (history15M.length === 0) {
      console.log(`No messages found in the last ${minute} minutes for chatflowId: ${chatflowId}.`)
      return
    } else {
      for (let index = 0; index < history15M.length; index++) {
        const element = history15M[index]
        const user = element.user
        const chatid = element.chatId
        const messages = element.messages
        if (messages?.length > 0) {
          refineFaqFromHistory(messages, chatid, chatflowId, timeLastCron, user)
        }
      }
    }
  } catch (error) {
    console.log('🚀 ~ index.ts:365 ~ cronJobRefineFaqFromHistory ~ error:', error)
  }
}

const getListOfFlowUsingFAQ = async () => {
  try {
    const appServer = getRunningExpressApp()
    const chatFlowRepo = appServer.AppDataSource.getRepository(ChatFlow)
    const flowsUsingFAQ = await chatFlowRepo
      .createQueryBuilder('chatFlow')
      .select('chatFlow.id')
      .where('chatFlow.isUseFAQ = :isUseFAQ OR chatFlow.showDashboard = :showDashboard', {
        isUseFAQ: true,
        showDashboard: true
      })
      .getRawMany()

    return flowsUsingFAQ
  } catch (error) {
    throw new InternalFlowiseError(
      StatusCodes.INTERNAL_SERVER_ERROR,
      `Error: faqsService.getListOfFlowUsingFAQ - ${getErrorMessage(error)}`
    )
  }
}

const fetchClassifyQARecords = async (req: any) => {
  try {
    let { chatflowId, minute } = req.body

    const appServer = getRunningExpressApp()
    const twentyMinutesAgo = new Date(Date.now() - minute * 60 * 1000).toISOString()

    const labelRecords = await appServer.AppDataSource.getRepository(Label).find({
      where: { chatflowid: chatflowId }
    })

    let list_label: string[] = []

    if (labelRecords.length > 0) {
      list_label = labelRecords.map((label) => label.label)
    } else {
      const recentRecords = await appServer.AppDataSource.getRepository(QA)
        .createQueryBuilder('qa')
        .where('qa.chatflowid = :chatflowId', { chatflowId })
        .getMany()
      if (recentRecords.length < 20) {
        console.log(`Chatflow ${chatflowId} does not have enough messages in the AQ list. Found: ${recentRecords.length}`)
        return
      }
    }

    const recentRecords = await appServer.AppDataSource.getRepository(QA)
      .createQueryBuilder('qa')
      .where('qa.createdDate > :time', { time: new Date(twentyMinutesAgo) })
      .orderBy('qa.createdDate', 'DESC')
      .getMany()

    const list_qa = recentRecords.map((qa) => ({
      question: qa.question,
      answer: qa.answer
    }))

    if (list_qa.length > 0) {
      await classifyAndUpdateLabels(chatflowId, list_qa, list_label)
    } else {
      console.log(`No AQ records have been saved in the AQ list for chatflowId: ${chatflowId} in the last ${minute} minutes.`)
    }

    return
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: fetchRecentQARecords - ${getErrorMessage(error)}`)
  }
}

const classifyAndUpdateLabels = async (
  chatflowId: string,
  list_qa: {
    question: string
    answer: string
  }[],
  list_label: string[]
) => {
  try {
    if (!chatflowId || !list_qa || !Array.isArray(list_qa)) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: classifyAndUpdateLabels - Missing or invalid parameters!')
    }

    const appServer = getRunningExpressApp()
    const textLabel = await classifyQuestions(list_qa, list_label)
    console.log('🚀 ~ index.ts:463 ~ textLabel:', textLabel)

    let arrayResponse: { question: string; label: string }[] = []
    try {
      arrayResponse = JSON.parse(textLabel)
    } catch (error) {
      console.log('🚀 ~ classifyAndUpdateLabels ~ error:', error)
    }

    if (arrayResponse?.length > 0) {
      const labelRepo = appServer.AppDataSource.getRepository(Label)
      const labelMap: { [key: string]: number } = {}

      arrayResponse.forEach((item) => {
        if (labelMap[item.label]) {
          labelMap[item.label] += 1
        } else {
          labelMap[item.label] = 1
        }
      })

      const labelUpdates = Object.entries(labelMap).map(async ([label, count]) => {
        const existingLabel = await labelRepo.findOne({
          where: { label, chatflowid: chatflowId }
        })

        if (existingLabel) {
          existingLabel.count = Number(existingLabel.count) + Number(count)
          return labelRepo.save(existingLabel)
        } else {
          return labelRepo.save(
            labelRepo.create({
              label,
              count,
              chatflowid: chatflowId
            })
          )
        }
      })

      await Promise.all(labelUpdates)
    }
  } catch (error) {
    console.log('🚀 ~ index.ts:492 ~ error:', error)
  }
}

const getRecentQARecords = async (req: any) => {
  try {
    const { chatflowId } = req.params

    if (!chatflowId) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: getRecentQARecords - chatflowId not provided!')
    }

    const appServer = getRunningExpressApp()
    const recentRecords = await appServer.AppDataSource.getRepository(Label)
      .createQueryBuilder('label')
      .where('label.chatflowid = :chatflowId', { chatflowId })
      .orderBy('label.createdDate', 'DESC')
      .getMany()

    const totalRecords = recentRecords.reduce((sum, record) => sum + Number(record.count), 0)

    const responseData = recentRecords.map((record) => ({
      category: record.label,
      total: Number(record.count),
      percent: parseFloat(((record.count / totalRecords) * 100).toFixed(1)),
      chatflowid: record.chatflowid,
      id: record.id
    }))

    return { data: responseData }
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: getRecentQARecords - ${getErrorMessage(error)}`)
  }
}

const addOrUpdateLabel = async (req: any) => {
  try {
    const { chatflowId, id, label } = req.body

    if (!chatflowId || !label) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: addOrUpdateLabel - chatflowId or label not provided!')
    }

    const appServer = getRunningExpressApp()
    const labelRepo = appServer.AppDataSource.getRepository(Label)

    let existingLabel = null
    if (id) {
      existingLabel = await labelRepo.findOne({
        where: { id, chatflowid: chatflowId }
      })

      if (!existingLabel) {
        throw new InternalFlowiseError(StatusCodes.NOT_FOUND, 'Error: addOrUpdateLabel - Label not found!')
      }
    }

    if (existingLabel) {
      existingLabel.label = label
      await labelRepo.save(existingLabel)
      return { message: 'Label updated successfully', label: existingLabel }
    } else {
      const newLabel = labelRepo.create({
        id: id || uuidv4(),
        label,
        count: 0,
        chatflowid: chatflowId
      })
      await labelRepo.save(newLabel)
      return { message: 'Label added successfully', label: newLabel }
    }
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: addOrUpdateLabel - ${getErrorMessage(error)}`)
  }
}

const removeLabel = async (req: any) => {
  try {
    const { chatflowId, id } = req.params

    if (!chatflowId || !id) {
      throw new InternalFlowiseError(StatusCodes.PRECONDITION_FAILED, 'Error: removeLabel - chatflowId or id not provided!')
    }

    const appServer = getRunningExpressApp()
    const labelRepo = appServer.AppDataSource.getRepository(Label)

    const existingLabel = await labelRepo.findOne({
      where: { id, chatflowid: chatflowId }
    })

    if (!existingLabel) {
      throw new InternalFlowiseError(StatusCodes.NOT_FOUND, 'Error: removeLabel - Label not found!')
    }

    await labelRepo.delete({ id, chatflowid: chatflowId })
    return { message: 'Label removed successfully' }
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Error: removeLabel - ${getErrorMessage(error)}`)
  }
}

const generateSystemPrompt = async (req: Request) => {
  try {
    const { promptDescription } = req.body
    const result = await generateSystemPromptModel(promptDescription)
    return { text: result }
  } catch (error) {
    throw new InternalFlowiseError(StatusCodes.INTERNAL_SERVER_ERROR, `Prompt generation failed: ${getErrorMessage(error)}`)
  }
}

export const faqsService = {
  saveFaq,
  importFaqs,
  getAllFaqs,
  getFaqById,
  updateFaq,
  deleteFaq,
  deleteAllFaqs,
  searchFaqs,
  deleteIndexService,
  updateSettingsService,
  vectorSearchFaqs,
  cronJobRefineFaqFromHistory,
  getListOfFlowUsingFAQ,
  fetchClassifyQARecords,
  getRecentQARecords,
  addOrUpdateLabel,
  removeLabel,
  generateSystemPrompt
}
