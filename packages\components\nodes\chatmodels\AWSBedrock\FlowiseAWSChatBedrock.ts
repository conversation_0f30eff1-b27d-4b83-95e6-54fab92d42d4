import { IVisionChatModal, IMultiModalOption } from '../../../src'
import { ChatBedrockConverse as LCBed<PERSON><PERSON>hat, ChatBedrockConverseInput } from '@langchain/aws'
import { StructuredToolInterface } from '@langchain/core/tools'

const DEFAULT_IMAGE_MODEL = 'anthropic.claude-3-haiku-20240307-v1:0'
const DEFAULT_IMAGE_MAX_TOKEN = 1024

export class BedrockChat extends LCBedrockChat implements IVisionChatModal {
  configuredModel: string
  configuredMaxToken?: number
  multiModalOption: IMultiModalOption
  id: string
  disableParallelToolUse: boolean = true

  constructor(id: string, fields: ChatBedrockConverseInput) {
    super(fields)
    this.id = id
    this.configuredModel = fields?.model || ''
    this.configuredMaxToken = fields?.maxTokens
  }

  revertToOriginalModel(): void {
    this.model = this.configuredModel
    this.maxTokens = this.configuredMaxToken
  }

  setMultiModalOption(multiModalOption: IMultiModalOption): void {
    this.multiModalOption = multiModalOption
  }

  setDisableParallelToolUse(disableParallelToolUse: boolean): void {
    this.disableParallelToolUse = disableParallelToolUse
  }

  setVisionModel(): void {
    if (!this.model.startsWith('claude-3')) {
      this.model = DEFAULT_IMAGE_MODEL
      this.maxTokens = this.configuredMaxToken ? this.configuredMaxToken : DEFAULT_IMAGE_MAX_TOKEN
    }
  }

  override bindTools(tools: StructuredToolInterface[], kwargs?: any) {
    // Pass the disableParallelToolUse parameter to the bind method
    const bindOptions = {
      ...kwargs,
      disable_parallel_tool_use: this.disableParallelToolUse
    }
    return super.bindTools(tools, bindOptions)
  }
}
