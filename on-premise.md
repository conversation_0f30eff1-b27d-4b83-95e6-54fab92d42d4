### Step 1: Setup các service lên On-Prem (Thuật + độ<PERSON>)

- Setup DB (postgres) / Redis
- Setup C-agent & <PERSON>ho tài liệu
- Setup Worker/Cronjob service (Trigger.dev)
- Setup Crawl service
- Setup Langfulse

### Step 2: <PERSON>yển kho tài li<PERSON>u sang On-Prem (đội Huy)

1. <PERSON><PERSON><PERSON> nối quản lý file sang MinIO thay vì AWS S3

   - Tích hợp kết nối theo ACCESS_KEY (hiện tại đang theo môi trường ec2, AWS sẽ tự auth thay vì dùng key)
   - <PERSON><PERSON><PERSON> hình MinIO endpoint, bucket trong mã nguồn và đảm bảo tính tương thích của đường dẫn tệp so với cấu hình S3
     hiện tại
   - Test toàn bộ các chức năng quản lý tệp (CRUD) trên MinIO để đảm bảo giống với s3 hiện tại

### Step 3: <PERSON><PERSON><PERSON><PERSON> OpenSearch sang On-Prem (đội Đông)

1. <PERSON><PERSON><PERSON> n<PERSON>i đến On-Prem OpenSearch (opensource API) để tạo Index thay vì AWS OpenSearch
   - Kiểm tra version tương thích với OpenSearch trên AWS
   - Cấu hình schema cho OpenSearch index và triển khai cơ chế tự động tạo index mới khi một Knowledge Base được khởi
     tạo

### Step 4: Chuyển AWS Bedrock Knowledge Base (KB) sang On-Prem (đội Đông)

1. Viết port API để xử lý logic KB (On-Prem KB) trên RAG-Flow giống với AWS Bedrock KB
   - API endpoint để kích hoạt quá trình đồng bộ (sync) dữ liệu từ MinIO vào OpenSearch, tích hợp pipeline trích xuất
     văn bản từ các định dạng file (ảnh, PDF, DOCX)
   - Logic chia nhỏ (chunking) văn bản và tạo vector embeddings (sử dụng model embedding on-prem) theo cấu hình tương
     tự AWS Bedrock KB, sau đó ingest dữ liệu đã xử lý vào OpenSearch
   - API retriever thực hiện tìm kiếm HYBRID, hỗ trợ lọc kết quả theo prefix và KB ID
2. Sửa node AWS KB Retriever để tương thích với On-Prem KB Retriever
   - Cập nhật giao diện người dùng của node (nếu cần) và sửa đổi logic backend để node có thể giao tiếp (gửi request,
     nhận response) với các API On-Prem KB đã phát triển

### Step 5: Triển khai các dịch vụ đi kèm trên On-Prem (đội Huy)

1. Crawl Service
   - Chuyển model xử lý ảnh (viết mô tả, OCR) đã crawl sang On-prem model
   - Chuyển model tạo metadata sang On-prem model
2. Static dashboard
   - Phân loại câu hỏi sang on-prem LLM
3. FAQ
   - Chuyển sang on-prem LLM

### Step 6: Chốt server

1. Cần chốt các services nào chạy trên server nào, mục đích gộp thành 2 server.
   - LLM
   - Storage, DB, Opensearch
   - C-Agent
   - Crawler, worker
