import config from '@/config'
import { baseURL } from '@/store/constant'
import axios from 'axios'

const dataLogin = localStorage.getItem('dataLogin') ? JSON?.parse(localStorage.getItem('dataLogin')) : {}
const accessToken = dataLogin?.accessToken || ''

const apiClient = axios.create({
  baseURL: `${baseURL}/api/v1`,
  headers: {
    'Content-type': 'application/json',
    ...(accessToken && { Authorization: `Bearer ${accessToken}` })
  }
})

apiClient.interceptors.request.use(function (config) {
  return config
})

apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response && error.response.data && error.response.data.error === 'Unauthorized Access') {
      localStorage.removeItem('dataLogin')
      window.location.href = config.basename ? config.basename + '/' : '/'
    }
    return Promise.reject(error)
  }
)

export default apiClient
