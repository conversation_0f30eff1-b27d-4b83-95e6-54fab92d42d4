import { z } from 'zod'
import { <PERSON>backMana<PERSON>, CallbackManagerForToolRun, Callbacks, parseCallbackConfigArg } from '@langchain/core/callbacks/manager'
import { BaseDynamicToolInput, DynamicTool, StructuredTool, ToolInputParsingException } from '@langchain/core/tools'
import { BaseRetriever } from '@langchain/core/retrievers'
import { ICommonObject, INode, INodeData, INodeParams } from '../../../src/Interface'
import { getBaseClasses, getCredentialData, getCredentialParam } from '../../../src/utils'
import { SOURCE_DOCUMENTS_PREFIX } from '../../../src/agents'
import { RunnableConfig } from '@langchain/core/runnables'
import { customGet } from '../../sequentialagents/commonUtils'
import { VectorStoreRetriever } from '@langchain/core/vectorstores'
import { createAwsOpenSearchClient } from '../../../src/openSearch'
import { extractPrefixFromSourceUri, fetchMultiplePrefixMetadata } from '../../../src/metadataFetcher'

const howToUse = `Add additional filters to vector store. You can also filter with flow config, including the current "state":
- \`$flow.sessionId\`
- \`$flow.chatId\`
- \`$flow.chatflowId\`
- \`$flow.input\`
- \`$flow.state\`
`

type ZodObjectAny = z.ZodObject<any, any, any, any>
type IFlowConfig = { sessionId?: string; chatId?: string; input?: string; state?: ICommonObject }
interface DynamicStructuredToolInput<T extends z.ZodObject<any, any, any, any> = z.ZodObject<any, any, any, any>>
  extends BaseDynamicToolInput {
  func?: (input: z.infer<T>, runManager?: CallbackManagerForToolRun, flowConfig?: IFlowConfig) => Promise<string>
  schema: T
  score?: Number
}

class DynamicStructuredTool<T extends z.ZodObject<any, any, any, any> = z.ZodObject<any, any, any, any>> extends StructuredTool<
  T extends ZodObjectAny ? T : ZodObjectAny
> {
  static lc_name() {
    return 'DynamicStructuredTool'
  }

  name: string

  description: string

  func: DynamicStructuredToolInput['func']

  // @ts-ignore
  schema: T

  private flowObj: any

  score?: Number

  constructor(fields: DynamicStructuredToolInput<T>) {
    super(fields)
    this.name = fields.name
    this.description = fields.description
    this.func = fields.func
    this.returnDirect = fields.returnDirect ?? this.returnDirect
    this.schema = fields.schema
    this.score = fields.score
  }

  async call(arg: any, configArg?: RunnableConfig | Callbacks, tags?: string[], flowConfig?: IFlowConfig): Promise<string> {
    const config = parseCallbackConfigArg(configArg)
    if (config.runName === undefined) {
      config.runName = this.name
    }
    let parsed
    try {
      parsed = await this.schema.parseAsync(arg)
    } catch (e) {
      throw new ToolInputParsingException(`Received tool input did not match expected schema`, JSON.stringify(arg))
    }
    const callbackManager_ = await CallbackManager.configure(
      config.callbacks,
      this.callbacks,
      config.tags || tags,
      this.tags,
      config.metadata,
      this.metadata,
      { verbose: this.verbose }
    )
    const runManager = await callbackManager_?.handleToolStart(
      this.toJSON(),
      typeof parsed === 'string' ? parsed : JSON.stringify(parsed),
      undefined,
      undefined,
      undefined,
      undefined,
      config.runName
    )
    let result
    try {
      result = await this._call(parsed, runManager, flowConfig)
    } catch (e) {
      await runManager?.handleToolError(e)
      throw e
    }
    if (result && typeof result !== 'string') {
      result = JSON.stringify(result)
    }
    await runManager?.handleToolEnd(result)
    return result
  }

  // @ts-ignore
  protected _call(arg: any, runManager?: CallbackManagerForToolRun, flowConfig?: IFlowConfig): Promise<string> {
    let flowConfiguration: ICommonObject = {}
    if (typeof arg === 'object' && Object.keys(arg).length) {
      for (const item in arg) {
        flowConfiguration[`$${item}`] = arg[item]
      }
    }

    // inject flow properties
    if (this.flowObj) {
      flowConfiguration['$flow'] = { ...this.flowObj, ...flowConfig }
    }

    return this.func!(arg as any, runManager, flowConfiguration)
  }

  setFlowObject(flow: any) {
    this.flowObj = flow
  }
}

class Retriever_Tools implements INode {
  label: string
  name: string
  version: number
  description: string
  type: string
  icon: string
  category: string
  baseClasses: string[]
  credential: INodeParams
  inputs: INodeParams[]
  score: number

  constructor() {
    this.label = 'Retriever Tool'
    this.name = 'retrieverTool'
    this.version = 3.0
    this.type = 'RetrieverTool'
    this.icon = 'retrievertool.svg'
    this.category = 'Tools'
    this.description = 'Use a retriever as allowed tool for agent'
    this.baseClasses = [this.type, 'DynamicTool', ...getBaseClasses(DynamicTool)]
    this.credential = {
      label: 'AWS Credential',
      name: 'credential',
      type: 'credential',
      credentialNames: ['awsApi'],
      optional: true
    }
    this.inputs = [
      {
        label: 'Retriever Name',
        name: 'name',
        type: 'string',
        placeholder: 'search_state_of_union'
      },
      {
        label: 'Retriever Description',
        name: 'description',
        type: 'string',
        description: 'When should agent uses to retrieve documents',
        rows: 3,
        placeholder: 'Searches and returns documents regarding the state-of-the-union.'
      },
      {
        label: 'Retriever',
        name: 'retriever',
        type: 'BaseRetriever'
      },
      {
        label: 'Return Full Document Content',
        name: 'returnFullContent',
        type: 'boolean',
        description: 'Whether to return the full content of retrieved documents',
        optional: true,
        default: false
      },
      {
        label: 'Include S3 Metadata',
        name: 'includeS3Metadata',
        type: 'boolean',
        description: "Whether to fetch S3 metadata and embed it at the end of each document's pageContent",
        optional: true,
        default: false
      },
      {
        label: 'OpenSearch Index',
        name: 'opensearchIndex',
        type: 'string',
        description: 'The OpenSearch index to query for full document content',
        optional: true,
        default: 'index-0',
        additionalParams: true
      },
      {
        label: 'Return Raw Document',
        name: 'returnRawDocument',
        type: 'boolean',
        description: 'Whether to return the raw document object from the retriever',
        optional: true,
        default: false
      },
      {
        label: 'Return Source Documents',
        name: 'returnSourceDocuments',
        type: 'boolean',
        optional: true
      },
      {
        label: 'Return Only Source Documents',
        name: 'returnOnlySourceDocuments',
        type: 'boolean',
        optional: true
      },
      {
        label: 'Additional Metadata Filter',
        name: 'retrieverToolMetadataFilter',
        type: 'json',
        description: 'Add additional metadata filter on top of the existing filter from vector store',
        optional: true,
        additionalParams: true,
        hint: {
          label: 'What can you filter?',
          value: howToUse
        }
      },
      {
        label: 'Score',
        name: 'score',
        type: 'number',
        optional: true,
        default: 0
      }
    ]
  }

  async init(nodeData: INodeData, _: string, options: ICommonObject): Promise<any> {
    const name = nodeData.inputs?.name as string
    const description = nodeData.inputs?.description as string
    const retriever = nodeData.inputs?.retriever as BaseRetriever
    const returnSourceDocuments = nodeData.inputs?.returnSourceDocuments as boolean
    const returnOnlySourceDocuments = nodeData.inputs?.returnOnlySourceDocuments as boolean

    const retrieverToolMetadataFilter = nodeData.inputs?.retrieverToolMetadataFilter
    const score = +nodeData.inputs?.score
    const input = {
      name,
      description,
      score
    }

    let credentials = { accessKeyId: '', secretAccessKey: '' }

    if (nodeData.credential) {
      const credentialData = await getCredentialData(nodeData.credential, options)
      const accessKeyId = getCredentialParam('awsKey', credentialData, nodeData)
      const secretAccessKey = getCredentialParam('awsSecret', credentialData, nodeData)

      if (accessKeyId && secretAccessKey) {
        credentials = {
          accessKeyId,
          secretAccessKey
        }
      }
    }

    const flow = { chatflowId: options.chatflowid }

    const func = async ({ input }: { input: string }, _?: CallbackManagerForToolRun, flowConfig?: IFlowConfig) => {
      if (retrieverToolMetadataFilter) {
        const flowObj = flowConfig

        const metadatafilter =
          typeof retrieverToolMetadataFilter === 'object' ? retrieverToolMetadataFilter : JSON.parse(retrieverToolMetadataFilter)
        const newMetadataFilter: any = {}
        for (const key in metadatafilter) {
          let value = metadatafilter[key]
          if (value.startsWith('$flow')) {
            value = customGet(flowObj, value)
          }
          newMetadataFilter[key] = value
        }

        const vectorStore = (retriever as VectorStoreRetriever<any>).vectorStore
        vectorStore.filter = newMetadataFilter
      }
      const docs = await retriever.invoke(input)

      if (nodeData.inputs?.returnFullContent && credentials.accessKeyId && credentials.secretAccessKey) {
        // Extract prefixes from docs
        const prefixes = docs
          .map((doc) => doc.metadata?.source?.replace('s3://cts-llm-docs-bucket/', ''))
          .filter((prefix): prefix is string => typeof prefix === 'string')

        // console.log('🚀 ~ RetrieverTool.ts:249 ~ Retriever_Tools ~ func ~ prefixes:', { docs, credentials })

        if (prefixes.length > 0) {
          try {
            const client = createAwsOpenSearchClient(credentials.accessKeyId, credentials.secretAccessKey)
            const opensearchIndex = (nodeData.inputs?.opensearchIndex as string) || 'index-0'

            // Track which docs were updated from OpenSearch
            const updatedDocs = new Set<string>()
            const contentMap = new Map<string, string>()
            const handledSources = new Set<string>([])

            // Query OpenSearch for each prefix to get all chunks
            for (const prefix of prefixes) {
              const sourceUri = `s3://${process.env.S3_STORAGE_BUCKET_NAME || 'cts-llm-docs-bucket'}/${prefix}`

              if (handledSources.has(sourceUri)) {
                continue
              }

              handledSources.add(sourceUri)

              try {
                // Search for all chunks with the given prefix
                const response = await client.search({
                  index: opensearchIndex,
                  body: {
                    query: {
                      match: {
                        'x-amz-bedrock-kb-source-uri': sourceUri
                      }
                    }
                  }
                })

                // console.log(
                //   'search:',
                //   `s3://${process.env.S3_STORAGE_BUCKET_NAME || 'cts-llm-docs-bucket'}/${prefix}`,
                //   response.body.hits.hits.length
                // )

                // Process search results
                if (response.body.hits.total.value > 0) {
                  // Combine all chunks for this prefix
                  const chunks = response.body.hits.hits.map((hit: any) => {
                    if (hit._source['x-amz-bedrock-kb-source-uri'] !== sourceUri) return ''
                    return hit._source.text || hit._source.content || hit._source['AMAZON_BEDROCK_TEXT_CHUNK'] || ''
                  })
                  const fullContent = chunks.join('\n\n').trim()

                  console.log('full content:', JSON.stringify(fullContent).substring(0, 100), fullContent.length)

                  // Store the combined content
                  contentMap.set(prefix, fullContent)
                }
              } catch (error) {
                console.error(`Error querying OpenSearch for prefix ${prefix}:`, error)
              }
            }

            // Update document content with full content from OpenSearch
            docs.forEach((doc) => {
              const prefix = doc.metadata?.source?.replace('s3://cts-llm-docs-bucket/', '')
              if (typeof prefix === 'string' && contentMap.has(prefix)) {
                doc.pageContent = contentMap.get(prefix) as string
                updatedDocs.add(doc.metadata?.source)
              }
            })

            // Filter out duplicate documents
            const uniqueSources = new Map()
            const uniqueDocs = docs.filter((doc) => {
              if (!updatedDocs.has(doc.metadata?.source)) {
                return true
              }
              if (!uniqueSources.has(doc.metadata?.source)) {
                uniqueSources.set(doc.metadata?.source, true)
                return true
              }
              return false
            })

            // Update original array in place
            docs.splice(0, docs.length, ...uniqueDocs)
          } catch (error) {
            console.error('Error fetching full content from OpenSearch:', error)
          }
        }
      }

      // Fetch and merge S3 metadata if enabled
      if (nodeData.inputs?.includeS3Metadata) {
        try {
          // Extract prefixes from all documents
          const prefixesFromDocs = docs
            .map((doc) => {
              const sourceUri = doc.metadata?.source || doc.metadata?.['x-amz-bedrock-kb-source-uri'] || ''
              return extractPrefixFromSourceUri(sourceUri)
            })
            .filter((prefix): prefix is string => typeof prefix === 'string' && prefix.length > 0)

          // Remove duplicates
          const uniquePrefixes = [...new Set(prefixesFromDocs)]

          if (uniquePrefixes.length > 0) {
            if (process.env.DEBUG === 'true') {
              console.info(`[RetrieverTool] Fetching metadata for ${uniquePrefixes.length} unique prefixes`)
            }

            // Fetch metadata for all unique prefixes
            const metadataMap = await fetchMultiplePrefixMetadata(uniquePrefixes)

            // Embed metadata into pageContent
            docs.forEach((doc) => {
              const sourceUri = doc.metadata?.source || doc.metadata?.['x-amz-bedrock-kb-source-uri'] || ''
              const prefix = extractPrefixFromSourceUri(sourceUri)

              if (prefix && metadataMap.has(prefix)) {
                const s3Metadata = metadataMap.get(prefix)!
                const metadataAttributes = s3Metadata.content.metadataAttributes

                // Create metadata text to append
                const metadataText = `

--- Document Metadata ---
Category: ${metadataAttributes.category}
Creator: ${metadataAttributes.creator}
Created At: ${metadataAttributes.createAt}
Access Level: ${metadataAttributes.AccessLevel}
AI Suggested Categories: ${metadataAttributes.SuggestedAICategories}
AI Summary: ${metadataAttributes.SummaryOfSuggestedAI}
AI Keywords: ${metadataAttributes.KeywordAISuggestion}
Version: ${metadataAttributes.version}
Availability Date: ${metadataAttributes.availabilityDate}
`

                // Append metadata to pageContent
                doc.pageContent = doc.pageContent + metadataText
              }
            })

            if (process.env.DEBUG === 'true') {
              console.info(`[RetrieverTool] Successfully merged metadata for ${metadataMap.size} prefixes`)
            }
          }
        } catch (error) {
          console.error('[RetrieverTool] Error fetching S3 metadata:', error)
          // Continue without metadata - don't fail the retrieval
        }
      }

      // Filter documents based on score threshold
      const filteredDocs = docs.filter((doc) => {
        // If score is not a number, include the document
        if (isNaN(+doc.metadata?.score)) return true
        // If score threshold is set (>= 0), only include docs that meet or exceed it
        if (score >= 0) return doc.metadata.score >= score
        // Include all docs if no valid score threshold
        return true
      })

      const content = nodeData.inputs?.returnRawDocument
        ? JSON.stringify(
            filteredDocs.map((doc) => ({
              pageContent: doc.pageContent,
              metadata: nodeData.inputs?.includeS3Metadata
                ? doc.metadata // Include all metadata when S3 metadata is enabled
                : {
                    score: doc.metadata.score,
                    sub_cate_1: doc.metadata?.sub_cate_1
                  }
            })),
            null,
            2
          )
        : filteredDocs.map((doc) => doc.pageContent).join('\n\n')

      const sourceDocuments = JSON.stringify(filteredDocs)

      if (returnOnlySourceDocuments) {
        return sourceDocuments
      }

      return returnSourceDocuments ? content + SOURCE_DOCUMENTS_PREFIX + sourceDocuments : content
    }

    const schema = z.object({
      input: z.string().describe('input to look up in retriever')
    }) as any
    const tool = new DynamicStructuredTool({ ...input, func, schema })
    tool.setFlowObject(flow)
    return tool
  }
}

module.exports = { nodeClass: Retriever_Tools }
