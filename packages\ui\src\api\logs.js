import client from './client'

const logsAPI = {
  // <PERSON><PERSON><PERSON> cả logs với filter
  getAllLogs: async (params = {}) => {
    const response = await client.get('/logs', { params })
    return response.data
  },

  // <PERSON><PERSON><PERSON> logs theo agent ID
  getLogsByAgent: async (agentId, params = {}) => {
    const response = await client.get(`/logs/agent/${agentId}`, { params })
    return response.data
  }
}

export default logsAPI
