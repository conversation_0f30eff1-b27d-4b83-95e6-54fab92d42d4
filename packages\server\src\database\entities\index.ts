import { ChatFlow } from './ChatFlow'
import { ChatMessage } from './ChatMessage'
import { ChatMessageFeedback } from './ChatMessageFeedback'
import { Credential } from './Credential'
import { Tool } from './Tool'
import { Assistant } from './Assistant'
import { Variable } from './Variable'
import { DocumentStore } from './DocumentStore'
import { DocumentStoreFileChunk } from './DocumentStoreFileChunk'
import { Lead } from './Lead'
import { UpsertHistory } from './UpsertHistory'
import { ApiKey } from './ApiKey'
import { CustomTemplate } from './CustomTemplate'
import { User } from './User'
import { GroupUsers } from './GroupUser'
import { QA } from './AQ'
import { Session } from './Session'
import { Label } from './Label'
import { ingestionJobsStatus } from './ingestionJobStatus'
import { KnowledgeBase } from './IngestionJobs'
import { Log } from './Log'

export const entities = {
  ChatFlow,
  ChatMessage,
  ChatMessageFeedback,
  Credential,
  Tool,
  Assistant,
  Variable,
  DocumentStore,
  DocumentStoreFileChunk,
  Lead,
  UpsertHistory,
  ApiKey,
  CustomTemplate,
  User,
  GroupUsers,
  QA,
  Session,
  Label,
  ingestionJobsStatus,
  KnowledgeBase,
  Log
}
