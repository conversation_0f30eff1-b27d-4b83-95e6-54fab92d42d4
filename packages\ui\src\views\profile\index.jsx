import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useParams, useLocation } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import moment from 'moment'
import {
  Box,
  Stack,
  FormControlLabel,
  Switch,
  Button,
  Tab,
  Tabs,
  Card,
  Table,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
  Typography,
  TableContainer,
  Paper
} from '@mui/material'
import { useNavigate } from 'react-router-dom'
import { IconX } from '@tabler/icons-react'
import { enqueueSnackbar as enqueueSnackbarAction, closeSnackbar as closeSnackbarAction } from '@/store/actions'
import { styled } from '@mui/material/styles'
import { tableCellClasses } from '@mui/material/TableCell'
import ViewHeader from '@/layout/MainLayout/ViewHeader'
import PropTypes from 'prop-types'
import useApi from '@/hooks/useApi'
import credentialsApi from '@/api/credentials'
import userApi from '@/api/user'
import Document from './Document'

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  borderColor: theme.palette.grey[900] + 25,
  padding: '6px 16px',
  [`&.${tableCellClasses.head}`]: { color: theme.palette.grey[900] },
  [`&.${tableCellClasses.body}`]: { fontSize: 14, height: 64 }
}))

const StyledTableRow = styled(TableRow)(() => ({
  '&:last-child td, &:last-child th': { border: 0 }
}))

const FUNCTION_BY_ROLE = {
  MASTER_ADMIN: [
    'Thêm group',
    'Sửa group',
    'Xóa group',
    'Thêm tài khoản',
    'Cập nhật tài khoản',
    'Xóa tài khoản',
    'Active/inactive tài khoản',
    'Phân quyền tài khoản',
    'Xem Chat Flow',
    'Tạo Chat Flow',
    'Sửa Chat Flow',
    'Phát hành Chat Flow',
    'Thiết lập Chat Flow',
    'Xem Agent',
    'Tạo Agent',
    'Sửa Agent',
    'Xóa Agent',
    'Phát hành Agent',
    'Thiết lập Agent',
    'Xem kho tài liệu',
    'Thêm tài liệu',
    'Sửa tài liệu',
    'Xóa tài liệu',
    'Cài đặt và công cụ'
  ],
  SITE_ADMIN: [
    'Thêm group',
    'Sửa group',
    'Xóa group',
    'Thêm tài khoản',
    'Cập nhật tài khoản',
    'Xóa tài khoản',
    'Active/inactive tài khoản',
    'Phân quyền tài khoản',
    'Xem Chat Flow',
    'Tạo Chat Flow',
    'Sửa Chat Flow',
    'Phát hành Chat Flow',
    'Thiết lập Chat Flow',
    'Xem Agent',
    'Tạo Agent',
    'Sửa Agent',
    'Xóa Agent',
    'Phát hành Agent',
    'Thiết lập Agent',
    'Xem kho tài liệu',
    'Thêm tài liệu',
    'Sửa tài liệu',
    'Xóa tài liệu',
    'Cài đặt và công cụ'
  ],
  ADMIN: [
    'Xem Chat Flow',
    'Tạo Chat Flow',
    'Sửa Chat Flow',
    'Phát hành Chat Flow',
    'Thiết lập Chat Flow',
    'Xem Agent',
    'Tạo Agent',
    'Sửa Agent',
    'Xóa Agent',
    'Phát hành Agent',
    'Thiết lập Agent',
    'Xem kho tài liệu',
    'Thêm tài liệu',
    'Sửa tài liệu',
    'Xóa tài liệu'
  ],
  USER: ['Xem Chat Flow', 'Xem Agent', 'Xem kho tài liệu']
}
const ROLE_PRIORITY = {
  MASTER_ADMIN: 4,
  SITE_ADMIN: 3,
  ADMIN: 2,
  USER: 1
}

const Profile = () => {
  const { id } = useParams()
  const location = useLocation()
  const { pathname } = location
  const navigate = useNavigate()
  const isProfileGroupPage = pathname.includes('/profile-group/')
  const [value, setValue] = useState(isProfileGroupPage ? 0 : 1)

  const currentUser = useSelector((state) => state.user)
  const isLogin = Boolean(currentUser?.id)

  const handleChangeTab = (event, newValue) => {
    setValue(newValue)
  }

  const dispatch = useDispatch()
  const enqueueSnackbar = (...args) => dispatch(enqueueSnackbarAction(...args))
  const closeSnackbar = (...args) => dispatch(closeSnackbarAction(...args))

  const getUser = useApi(userApi.getUserById)
  const { data: user, loading: isLoadingUser } = getUser

  const getGroupName = useApi(userApi.getGroupById)
  const { data: groupName, loading: isLoadingGroup } = getGroupName

  useEffect(() => {
    if (id && isLogin) {
      if (isProfileGroupPage) {
        getGroupName.request(id)
      } else {
        getUser.request(id)
        // getCredentials.request(id)
      }
    }
  }, [id, isProfileGroupPage])

  useEffect(() => {
    // Navigate về home page khi vào trang xem profile của user và role < group admin hoặc xem profile của group và role < site admin
    const currentRole = currentUser?.role

    if (ROLE_PRIORITY[currentRole] === 3 || ROLE_PRIORITY[currentRole] === 4) return

    if (ROLE_PRIORITY[currentRole] === 2) {
      if (user?.groupname && currentUser?.groupname && user?.groupname !== currentUser?.groupname) {
        navigate('/agentflows')
      }
    }

    if (ROLE_PRIORITY[currentRole] === 1) {
      if (user?.id && currentUser?.id && user?.id !== currentUser?.id) {
        navigate('/agentflows')
      }
    }
  }, [currentUser, isProfileGroupPage, groupName, user])

  const functions = FUNCTION_BY_ROLE[user?.role] || []

  const canEditActiveStatus = ROLE_PRIORITY[currentUser?.role] > ROLE_PRIORITY[user?.role]

  const handleToggleActive = async () => {
    try {
      const resUpdateUser = await userApi.updateUser(user.id, { active: !user.active })
      const resData = resUpdateUser?.data
      if (resData) {
        await getUser.request(id)
        return enqueueSnackbar({
          message: 'User đã cập nhật thành công.',
          options: {
            variant: 'success',
            action: (key) => (
              <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                <IconX />
              </Button>
            )
          }
        })
      } else {
        const msg = error?.response?.data?.message || 'Update user thất bại.'
        return enqueueSnackbar({
          message: msg,
          options: {
            variant: 'error',
            action: (key) => (
              <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
                <IconX />
              </Button>
            )
          }
        })
      }
    } catch (error) {
      const msg = error?.response?.data?.message ? error.response.data.message : 'Update user thất bại.'
      return enqueueSnackbar({
        message: msg,
        options: {
          variant: 'error',
          action: (key) => (
            <Button style={{ color: 'white' }} onClick={() => closeSnackbar(key)}>
              <IconX />
            </Button>
          )
        }
      })
    }
  }

  if (isLoadingUser || isLoadingGroup) return <div className='text-center text-gray-500'>Đang tải...</div>
  if (!user && !groupName) return <div className='text-center text-red-500'>Không tìm thấy người dùng</div>

  return (
    <div className='p-4 md:p-8 bg-gray-50 h-[calc(100dvh-152px)]'>
      {isLogin && isProfileGroupPage ? (
        <div>
          <div className='flex flex-wrap gap-4 items-center mb-6'>
            <p className='text-lg font-semibold'>
              <strong>Tên nhóm:</strong> {groupName.groupname}
            </p>
          </div>
        </div>
      ) : (
        <div className='flex flex-wrap gap-4 items-center mb-6'>
          <p className='text-lg font-semibold'>
            <strong>Tên người dùng:</strong> {user.username}
          </p>
          <p className='text-lg font-semibold'>
            <strong>Vai trò:</strong> {user.role}
          </p>
          <p style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <strong>Trạng thái:</strong>{' '}
            <span style={{ color: user.active ? 'green' : 'red' }}>{user.active ? 'Đang hoạt động' : 'Dừng hoạt động'}</span>
            {canEditActiveStatus && (
              <FormControlLabel control={<Switch checked={user.active} onChange={handleToggleActive} color='primary' />} label='' />
            )}
          </p>
        </div>
      )}
      <hr className='border-gray-300 mb-6' />
      {isProfileGroupPage ? (
        <Stack flexDirection='column' sx={{ gap: 3 }}>
          <ViewHeader
            title={
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs
                  value={value}
                  onChange={handleChangeTab}
                  aria-label='basic tabs example'
                  variant='scrollable'
                  scrollButtons='auto'
                  className='text-gray-700'
                >
                  <Tab label='Thông tin' {...a11yProps(0)} />
                  <Tab label='Tài liệu được truy cập' {...a11yProps(1)} />
                </Tabs>
              </Box>
            }
          />
          <CustomTabPanel value={value} index={0}>
            {isLogin ? (
              isProfileGroupPage ? (
                <>
                  <Card className='p-6 shadow-lg rounded-lg'>
                    <Typography variant='h5' className='mb-4 font-bold text-gray-800'>
                      Thông tin nhóm
                    </Typography>
                    <TableContainer component={Paper} className='border border-gray-300 rounded-lg'>
                      <Table aria-label='group details'>
                        <TableBody>
                          <StyledTableRow>
                            <StyledTableCell>
                              <strong>Tên nhóm:</strong>
                            </StyledTableCell>
                            <StyledTableCell>{groupName?.groupname}</StyledTableCell>
                          </StyledTableRow>
                          <StyledTableRow>
                            <StyledTableCell>
                              <strong>Ngày tạo:</strong>
                            </StyledTableCell>
                            <StyledTableCell>{moment(groupName?.createdDate).format('DD MM YYYY')}</StyledTableCell>
                          </StyledTableRow>
                        </TableBody>
                      </Table>
                    </TableContainer>

                    <Typography variant='h5' className='mt-6 mb-4 font-bold text-gray-800'>
                      Danh sách người dùng
                    </Typography>
                    <TableContainer component={Paper} className='border border-gray-300 rounded-lg'>
                      <Table aria-label='user list'>
                        <TableHead className='bg-gray-100'>
                          <TableRow>
                            <StyledTableCell>Tên người dùng</StyledTableCell>
                            <StyledTableCell>Email</StyledTableCell>
                            <StyledTableCell>Vai trò</StyledTableCell>
                            <StyledTableCell>Trạng thái</StyledTableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {groupName?.users?.length === 0 ? (
                            <StyledTableRow>
                              <StyledTableCell colSpan={4} align='center'>
                                Không có người dùng nào trong nhóm
                              </StyledTableCell>
                            </StyledTableRow>
                          ) : (
                            groupName.users.map((user, index) => (
                              <StyledTableRow key={index}>
                                <StyledTableCell>{user.username}</StyledTableCell>
                                <StyledTableCell>{user.email}</StyledTableCell>
                                <StyledTableCell>{user.role}</StyledTableCell>
                                <StyledTableCell>
                                  <span style={{ color: user.active ? 'green' : 'red' }}>
                                    {user.active ? 'Đang hoạt động' : 'Dừng hoạt động'}
                                  </span>
                                </StyledTableCell>
                              </StyledTableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </Card>
                </>
              ) : (
                <>
                  <Card className='p-6 shadow-lg rounded-lg mt-6'>
                    <Typography variant='h5' className='font-bold text-gray-800'>
                      Các chức năng có thể thực hiện
                    </Typography>
                    <ul className='list-disc pl-5 mt-3'>
                      {functions.map((func, idx) => (
                        <li key={idx} className='text-gray-700'>
                          <Typography variant='body1'>{func}</Typography>
                        </li>
                      ))}
                    </ul>
                  </Card>
                </>
              )
            ) : (
              <div className='text-center text-gray-500'>Đăng nhập để xem thông tin</div>
            )}
          </CustomTabPanel>
          <CustomTabPanel value={value} index={1}>
            {isLogin ? (
              <Document isGroupPage={isProfileGroupPage} displayPrefixes={user?.displayPrefixes || groupName?.displayPrefixes} />
            ) : (
              <div className='text-center text-gray-500'>Đăng nhập để xem thông tin</div>
            )}
          </CustomTabPanel>
        </Stack>
      ) : isLogin ? (
        <Document isGroupPage={isProfileGroupPage} displayPrefixes={user?.displayPrefixes || groupName?.displayPrefixes} />
      ) : (
        <div className='text-center text-gray-500'>Đăng nhập để xem thông tin</div>
      )}
    </div>
  )
}

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    'aria-controls': `simple-tabpanel-${index}`
  }
}

function CustomTabPanel(props) {
  const { children, value, index, ...other } = props
  return (
    <div role='tabpanel' hidden={value !== index} id={`simple-tabpanel-${index}`} aria-labelledby={`simple-tab-${index}`} {...other}>
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  )
}

CustomTabPanel.propTypes = {
  children: PropTypes.node,
  value: PropTypes.number.isRequired,
  index: PropTypes.number.isRequired
}

Profile.propTypes = {
  isGroupPage: PropTypes.bool
}

export default Profile
