import { <PERSON><PERSON><PERSON>, Column, CreateDateColumn, UpdateDateColumn, PrimaryGeneratedColumn } from 'typeorm'

@Entity()
export class QA {
  @PrimaryGeneratedColumn('uuid')
  id: string

  @Column({ type: 'uuid', nullable: true })
  userId: string

  @Column({ type: 'text', nullable: true })
  userName: string

  @Column()
  question: string

  @Column({ type: 'text' })
  answer: string

  @Column({ type: 'text' })
  question_type: string

  @Column({ type: 'uuid', nullable: true })
  chatId: string

  @Column({ type: 'uuid' })
  chatflowid: string

  @Column({ nullable: true })
  being_processed: boolean

  @Column({ type: 'timestamp' })
  @CreateDateColumn()
  timeLastCron: Date

  @Column({ type: 'timestamp' })
  @CreateDateColumn()
  createdDate: Date

  @Column({ type: 'timestamp' })
  @UpdateDateColumn()
  updatedDate: Date
}
