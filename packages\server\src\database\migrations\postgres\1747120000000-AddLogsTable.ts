import { MigrationInterface, QueryRunner } from 'typeorm'

export class AddLogsTable1747120000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE IF NOT EXISTS "logs" (
                "id" SERIAL PRIMARY KEY,
                "timestamp" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                "user_id" uuid NOT NULL,
                "action" text NOT NULL,
                "target_type" text NOT NULL,
                "target_id" uuid,
                "target_name" text NOT NULL,
                "username" text NOT NULL,
                "changes" jsonb,
                "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
              );`
    )

    // Create indexes for better query performance
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_logs_timestamp" ON "logs" ("timestamp");`)
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_logs_user_id" ON "logs" ("user_id");`)
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_logs_action" ON "logs" ("action");`)
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_logs_target_type" ON "logs" ("target_type");`)
    await queryRunner.query(`CREATE INDEX IF NOT EXISTS "IDX_logs_username" ON "logs" ("username");`)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE IF EXISTS "logs";`)
  }
}
