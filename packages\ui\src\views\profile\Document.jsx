import { S3Explorer } from 'dccxx-s3-explorer'
import 'dccxx-s3-explorer/dist/style.css'
import { useParams } from 'react-router'
import PropTypes from 'prop-types'

import.meta.env.VITE_DOCUMENT_STORE_TYPE = 'default'
import.meta.env.VITE_DOCUMENT_STORE_BASE_URL = import.meta.env.VITE_DOCUMENT_STORE_BASE_URL || '/s3-explorer'
const Document = ({ isGroupPage, displayPrefixes }) => {
  const { id } = useParams()
  const displayPrefixesArray = displayPrefixes ? JSON?.parse(displayPrefixes.replace(/'/g, '"')) : []

  return (
    <div className='p-4 space-y-6 md:space-y-8 lg:space-y-10'>
      {!displayPrefixesArray || displayPrefixesArray.length === 0 ? (
        <div className='flex flex-col items-center justify-center text-center space-y-4'>
          <p className='text-gray-500 text-lg md:text-xl'>Không có tài liệu được gán riêng cho {isGroupPage ? 'Nhóm' : 'Người dùng'}.</p>
        </div>
      ) : (
        <S3Explorer
          apiBaseUrl={import.meta.env.VITE_DOCUMENT_STORE_BASE_URL}
          homeLabel='Kho tài liệu'
          rootPrefix=''
          displayPrefixes={displayPrefixesArray}
          idCreator={(isGroupPage ? 'groups/' : 'users/') + id + '/'}
        />
      )}
    </div>
  )
}

Document.propTypes = {
  isGroupPage: PropTypes.bool,
  displayPrefixes: PropTypes.string
}

export default Document
