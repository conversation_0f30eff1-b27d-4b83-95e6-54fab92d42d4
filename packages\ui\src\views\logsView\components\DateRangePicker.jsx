import { useState, useRef, useEffect } from 'react'
import { TextField, Popover, Box, Button, ButtonGroup, Typography, Divider, Grid } from '@mui/material'
import { DayPicker } from 'react-day-picker'
import { CalendarToday } from '@mui/icons-material'
import 'react-day-picker/style.css'
import PropTypes from 'prop-types'

const DateRangePicker = ({
  value,
  onChange,
  label = 'Chọn thời gian',
  placeholder = 'Chọn ngày hoặc khoảng thời gian',
  fullWidth = true
}) => {
  const [anchorEl, setAnchorEl] = useState(null)
  const [mode, setMode] = useState('range') // 'single' | 'range'
  const [selectedDate, setSelectedDate] = useState(null)
  const [selectedRange, setSelectedRange] = useState(null)
  const inputRef = useRef(null)

  // Format date for display
  const formatDate = (date) => {
    if (!date) return ''
    return date.toLocaleDateString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  // Format date for API (YYYY-MM-DD)
  const formatDateForAPI = (date) => {
    if (!date) return ''
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // Get display value
  const getDisplayValue = () => {
    if (mode === 'single' && selectedDate) {
      return formatDate(selectedDate)
    }
    if (mode === 'range' && selectedRange) {
      if (selectedRange.from && selectedRange.to) {
        return `${formatDate(selectedRange.from)} - ${formatDate(selectedRange.to)}`
      }
      if (selectedRange.from) {
        return `${formatDate(selectedRange.from)} - ...`
      }
    }
    return ''
  }

  // Handle input click
  const handleInputClick = (event) => {
    setAnchorEl(event.currentTarget)
  }

  // Handle popover close
  const handleClose = () => {
    setAnchorEl(null)
  }

  // Handle mode change
  const handleModeChange = (newMode) => {
    setMode(newMode)
    setSelectedDate(null)
    setSelectedRange(null)
  }

  // Handle single date select
  const handleSingleDateSelect = (date) => {
    setSelectedDate(date)
  }

  // Handle range select
  const handleRangeSelect = (range) => {
    setSelectedRange(range)
  }

  // Handle apply
  const handleApply = () => {
    if (mode === 'single' && selectedDate) {
      const formattedDate = formatDateForAPI(selectedDate)
      onChange({
        dateType: 'single',
        date: formattedDate,
        fromDate: '',
        toDate: ''
      })
    } else if (mode === 'range' && selectedRange) {
      onChange({
        dateType: 'range',
        date: '',
        fromDate: selectedRange.from ? formatDateForAPI(selectedRange.from) : '',
        toDate: selectedRange.to ? formatDateForAPI(selectedRange.to) : ''
      })
    }
    handleClose()
  }

  // Handle clear
  const handleClear = () => {
    setSelectedDate(null)
    setSelectedRange(null)
    onChange({
      dateType: 'range',
      date: '',
      fromDate: '',
      toDate: ''
    })
    handleClose()
  }

  // Initialize from value prop
  useEffect(() => {
    if (value) {
      if (value.dateType === 'single' && value.date) {
        setMode('single')
        setSelectedDate(new Date(value.date))
      } else if (value.dateType === 'range') {
        setMode('range')
        const range = {}
        if (value.fromDate) range.from = new Date(value.fromDate)
        if (value.toDate) range.to = new Date(value.toDate)
        if (range.from || range.to) {
          setSelectedRange(range)
        }
      }
    }
  }, [value])

  const open = Boolean(anchorEl)

  return (
    <>
      <TextField
        ref={inputRef}
        fullWidth={fullWidth}
        label={label}
        placeholder={placeholder}
        value={getDisplayValue()}
        onClick={handleInputClick}
        InputProps={{
          readOnly: true,
          endAdornment: <CalendarToday color='action' />
        }}
        sx={{ cursor: 'pointer' }}
      />

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left'
        }}
        PaperProps={{
          sx: { p: 2, minWidth: 350 }
        }}
      >
        <Box>
          {/* Mode selector */}
          <Box sx={{ mb: 2 }}>
            <Typography variant='subtitle2' gutterBottom>
              Chọn kiểu lọc thời gian
            </Typography>
            <ButtonGroup size='small' fullWidth>
              <Button variant={mode === 'single' ? 'contained' : 'outlined'} onClick={() => handleModeChange('single')}>
                Ngày đơn
              </Button>
              <Button variant={mode === 'range' ? 'contained' : 'outlined'} onClick={() => handleModeChange('range')}>
                Khoảng thời gian
              </Button>
            </ButtonGroup>
          </Box>

          <Divider sx={{ mb: 2 }} />

          {/* Date picker */}
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              mb: 2,
              '& .rdp': {
                margin: 0,
                fontSize: '14px'
              },
              '& .rdp-months': {
                display: 'flex',
                justifyContent: 'center'
              },
              '& .rdp-day_selected': {
                backgroundColor: 'primary.main',
                color: 'primary.contrastText'
              },
              '& .rdp-day_range_middle': {
                backgroundColor: 'primary.light',
                color: 'primary.contrastText'
              }
            }}
          >
            {mode === 'single' ? (
              <DayPicker mode='single' selected={selectedDate} onSelect={handleSingleDateSelect} showOutsideDays fixedWeeks />
            ) : (
              <DayPicker mode='range' selected={selectedRange} onSelect={handleRangeSelect} showOutsideDays fixedWeeks />
            )}
          </Box>

          {/* Action buttons */}
          <Grid container spacing={1}>
            <Grid item xs={6}>
              <Button fullWidth variant='outlined' onClick={handleClear}>
                Xóa
              </Button>
            </Grid>
            <Grid item xs={6}>
              <Button
                fullWidth
                variant='contained'
                onClick={handleApply}
                disabled={(mode === 'single' && !selectedDate) || (mode === 'range' && !selectedRange?.from)}
              >
                Áp dụng
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Popover>
    </>
  )
}
DateRangePicker.propTypes = {
  value: PropTypes.shape({
    dateType: PropTypes.string,
    date: PropTypes.string,
    fromDate: PropTypes.string,
    toDate: PropTypes.string
  }),
  onChange: PropTypes.func.isRequired,
  label: PropTypes.string,
  placeholder: PropTypes.string,
  fullWidth: PropTypes.bool
}

export default DateRangePicker
